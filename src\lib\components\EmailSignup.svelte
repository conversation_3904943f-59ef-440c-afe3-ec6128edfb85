<script>
	import Modal from './Modal.svelte';
	import SuccessCheckmark from './SuccessCheckmark.svelte';
	import LoadingSpinner from './LoadingSpinner.svelte';

	let email = '';
	let message = '';
	let isSubmitting = false;
	let isValid = true;
	let showSuccessModal = false;
	let successMessage = '';
	let emailFocused = false;

	// Validate email as user types
	function validateEmail() {
		if (!email) {
			isValid = true; // Don't show error when empty
			return;
		}

		isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
	}

	function handleFocus() {
		emailFocused = true;
	}

	function handleBlur() {
		emailFocused = false;
		validateEmail();
	}

	function handleInput() {
		if (emailFocused) {
			validateEmail();
		}

		// Clear any error message when user starts typing again
		if (message.includes('error') || message.startsWith('Please')) {
			message = '';
		}
	}

	async function handleSubmit() {
		// Client-side validation
		validateEmail();
		if (!email || !isValid) {
			message = 'Please enter a valid email address.';
			return;
		}

		isSubmitting = true;
		message = 'Subscribing...';

		try {
			// Call our API endpoint
			const response = await fetch('/api/subscribe', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ email })
			});

			const result = await response.json();

			if (result.success) {
				// Show success modal instead of just a message
				successMessage = result.message;
				showSuccessModal = true;
				email = ''; // Clear input on success
				message = ''; // Clear the inline message
			} else {
				message = result.message || 'An error occurred. Please try again.';
			}
		} catch (error) {
			console.error('Error subscribing:', error);
			message = 'An error occurred. Please try again later.';
		} finally {
			isSubmitting = false;
		}
	}

	function closeSuccessModal() {
		showSuccessModal = false;
	}
</script>

<div class="mt-0 w-full max-w-md px-2 sm:px-0">
	<p class="mb-3 sm:mb-4 text-center text-base sm:text-lg text-slate-300">
		Be the first to know when Anithing.moe launches!
	</p>
	<form
		onsubmit={(e) => {
			e.preventDefault();
			handleSubmit();
		}}
		class="flex flex-col gap-3 sm:flex-row"
	>
		<div class="relative flex-grow">
			<input
				type="email"
				bind:value={email}
				oninput={handleInput}
				onfocus={handleFocus}
				onblur={handleBlur}
				placeholder="<EMAIL>"
				required
				class="w-full rounded-lg border bg-gray-800 px-3 sm:px-4 py-2.5 sm:py-3 transition-colors duration-200
                 {!isValid && emailFocused ? 'border-red-500' : 'border-gray-700'}
                 {isValid && email && emailFocused ? 'border-green-500' : ''}
                 text-slate-100 placeholder-gray-500 outline-none
                 focus:border-sky-500 focus:ring-2 focus:ring-sky-500 text-sm sm:text-base"
				disabled={isSubmitting}
			/>
			{#if !isValid && email && emailFocused}
				<div class="absolute top-1/2 right-3 -translate-y-1/2 text-red-500">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-4 w-4 sm:h-5 sm:w-5"
						viewBox="0 0 20 20"
						fill="currentColor"
					>
						<path
							fill-rule="evenodd"
							d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
							clip-rule="evenodd"
						/>
					</svg>
				</div>
			{/if}
			{#if isValid && email && emailFocused}
				<div class="absolute top-1/2 right-3 -translate-y-1/2 text-green-500">
					<svg
						xmlns="http://www.w3.org/2000/svg"
						class="h-4 w-4 sm:h-5 sm:w-5"
						viewBox="0 0 20 20"
						fill="currentColor"
					>
						<path
							fill-rule="evenodd"
							d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
							clip-rule="evenodd"
						/>
					</svg>
				</div>
			{/if}
		</div>
		<button
			type="submit"
			class="flex min-w-[110px] sm:min-w-[120px] cursor-pointer items-center justify-center rounded-lg bg-sky-600 px-4 sm:px-6
               py-2.5 sm:py-3 font-semibold text-white text-sm sm:text-base transition-colors
               duration-200 hover:bg-sky-700 disabled:cursor-not-allowed disabled:opacity-50"
			disabled={isSubmitting}
		>
			{#if isSubmitting}
				<span class="mr-2"><LoadingSpinner size={18} /></span>
				<span>Joining...</span>
			{:else}
				Get Updates
			{/if}
		</button>
	</form>
	{#if message}
		<p
			class="animate-fadeIn mt-2 sm:mt-3 text-center text-xs sm:text-sm"
			class:text-green-400={message.startsWith('Thanks') || message.includes('already subscribed')}
			class:text-red-400={message.startsWith('Please') || message.includes('error')}
			class:text-yellow-400={message.startsWith('Subscribing')}
		>
			{message}
		</p>
	{/if}
</div>

<!-- Success Modal -->
<Modal bind:show={showSuccessModal} title="Subscription Successful!" closeOnClickOutside={true}>
	<div class="flex flex-col items-center text-center">
		<div class="mb-3 sm:mb-4">
			<SuccessCheckmark size={60} />
		</div>
		<p class="mb-3 sm:mb-4 text-slate-200">{successMessage}</p>
		<p class="text-xs sm:text-sm text-slate-300">
			We'll keep you updated on all the exciting developments at Anithing.moe!
		</p>
	</div>

	<svelte:fragment slot="actions">
		<button
			class="cursor-pointer rounded-lg bg-sky-600 px-4 sm:px-6 py-1.5 sm:py-2 text-sm sm:text-base font-semibold text-white transition-colors hover:bg-sky-700"
			onclick={closeSuccessModal}
		>
			Great!
		</button>
	</svelte:fragment>
</Modal>
